<template>
  <a-input-group compact class="!flex w-240px">
    <a-input v-model:value="item.value" :placeholder="item.label + '，回车批量筛选'" allow-clear @blur="item.value = `${item.value || ''}`.trim()" @keyup.enter="handleShowBatchModal" />
    <a-button @click="handleShowBatchModal" class="!flex items-center justify-center !w-40px">
      <template #icon>
        <span class="iconfont icon-chazhao_find text-14px c-#000"></span>
      </template>
    </a-button>
  </a-input-group>
  <a-modal v-model:open="visible" title="批量输入" okText="确定" @ok="handleCloseBatchModal" @cancel="visible = false" class="top-200px">
    <a-form layout="vertical">
      <a-form-item :label="label">
        <a-textarea v-model:value="text" :placeholder="`多个${label}，以逗号分隔或每行一个${label}`" :rows="10" />
      </a-form-item>
    </a-form>
    <template #footer>
      <a-button class="float-left" @click="text = ''">清空</a-button>
      <a-button @click="visible = false">取消</a-button>
      <a-button type="primary" @click="handleCloseBatchModal">确认</a-button>
    </template>
  </a-modal>
</template>

<script setup lang="ts">
import { FormItemType } from '../type'

defineEmits<{
  (e: 'serach'): void
}>()

const item = defineModel<FormItemType<'input'>>('item', { required: true })

const label = ref<null | string>(null)
// 显示/隐藏
const visible = ref(false)
// 内容
const text = ref('')

// 显示批量弹窗
const handleShowBatchModal = () => {
  visible.value = true
  text.value = item.value.value
  label.value = item.value.label.replace('，回车批量筛选', '')
}

// 关闭批量弹窗
const handleCloseBatchModal = () => {
  item.value.value = transformText()
  visible.value = false
}

// 转化数据
const transformText = () => {
  let val = text.value
  if (val) {
    val = val.replace(/\n/g, ',')
    val = val.replace(/，/g, ',')
    val = val.replace(/;/g, ',')
    val = val.replace(/；/g, ',')
    let arr: string[] = []
    let str = ''
    arr = val.split(',')
    arr.forEach((it: string) => {
      if (it) {
        str += `${it.trim()},`
      }
    })
    str = str.slice(0, -1)
    return str
  }
}
</script>

<style scoped></style>
