<template>
  <a-drawer width="1200" @close="handleClose" v-model:open="openDrawer">
    <template #title>
      <div class="flex justify-between items-center">
        <span>发货单详情</span>
        <div class="flex gap-2">
          <a-button @click="handleChangeInfo('prev')">上一条</a-button>
          <a-button @click="handleChangeInfo('next')" class="ml-5">下一条</a-button>
        </div>
      </div>
    </template>
    <a-form ref="formRef" :model="shipmentDetails" :label-col="{ style: { width: '100px' } }" :wrapper-col="{ span: 17 }" layout="vertical" :rules="rules">
      <div class="drawer-title">发货单基本信息</div>
      <a-row :gutter="24" class="pl-20 pr-20">
        <a-col :span="6">
          <a-form-item label="预约单号">
            <span>{{ shipmentDetails.booking_order_number || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="关联采购单号">
            <span>{{ shipmentDetails.purchase_order_numbers?.join(', ') || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="收货仓库">
            <span>{{ shipmentDetails.warehouse_name || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="供应商">
            <span>{{ shipmentDetails.company_supplier_name || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="发货单状态">
            <span>{{ shipmentDetails.audit_status_str || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="预计到货日期">
            <span>{{ shipmentDetails.scheduled_arrival_time || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="创建人">
            <span>{{ shipmentDetails.creator_name || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="创建时间">
            <span>{{ shipmentDetails.create_at || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="实际到货时间">
            <span>{{ shipmentDetails.actual_arrival_time || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="6">
          <a-form-item label="到货凭证">
            <a-button v-if="shipmentDetails.files" type="text" @click="onClickFiles">查看附件</a-button>
            <span v-else>-</span>
          </a-form-item>
        </a-col>
      </a-row>
      <div class="drawer-title">收货信息</div>
      <a-row :gutter="24" class="pl-20 pr-20">
        <a-col :span="8">
          <a-form-item label="收货人">
            <span>{{ shipmentDetails.warehouse_receiver_name || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="联系电话">
            <span>{{ shipmentDetails.warehouse_receiver_phone || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="收货地址">
            <span>{{ shipmentDetails.receiver_address || '-' }}</span>
          </a-form-item>
        </a-col>
      </a-row>
      <div class="drawer-title">物流信息</div>
      <a-row :gutter="24" class="pl-20 pr-20">
        <a-col :span="8">
          <a-form-item label="物流公司" name="logistics_company_number">
            <a-select v-if="formType == 'editLogist'" placeholder="请选择物流公司" v-model:value="shipmentDetails.logistics_company_number">
              <a-select-option v-for="item in logisticsCompanyList" :key="item.id" :value="item.company_no">{{ item.company_name }}</a-select-option>
            </a-select>
            <span v-else>{{ shipmentDetails.logistics_company || '-' }}</span>
          </a-form-item>
        </a-col>
        <a-col :span="8">
          <a-form-item label="物流单号" name="tracking_number">
            <a-input v-if="formType == 'editLogist'" placeholder="请输入物流单号" v-model:value="shipmentDetails.tracking_number"></a-input>
            <span v-else>{{ shipmentDetails.logistics_number || '-' }}</span>
          </a-form-item>
        </a-col>
      </a-row>
      <div class="drawer-title">发货&入库明细</div>
      <a-table :columns="columns" :data-source="tableData" bordered scroll :pagination="false" class="!text-12px overflow-x-auto" size="small">
        <template #bodyCell="{ column, text, record }">
          <template v-if="column.dataIndex === 'sku_name'">
            <div class="flex">
              <BaseImage :src="record.image_url" :width="60" :height="60" />
              <div class="flex flex-col ml-4">
                <div class="lh-20">{{ record.sku_name }}</div>
                <div class="lh-20">
                  <span class="c-#999">类目：</span>
                  <span>{{ record.all_category || '--' }}</span>
                </div>
                <div class="lh-20">
                  <span class="c-#999">规格：</span>
                  <span>{{ record.type_specification || '--' }}</span>
                </div>
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'srs_supplier_prod_code'">
            <div>
              <div>
                <span class="c-#999">供应商：</span>
                <span>{{ record.srs_supplier_prod_code || '--' }}</span>
              </div>
              <div>
                <span class="c-#999">平台：</span>
                <span>{{ record.srs_platform_prod_code || '--' }}</span>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </a-form>
    <template #footer v-if="formType == 'editLogist'">
      <a-button type="primary" @click="onSubmit">确定</a-button>
      <a-button style="margin-right: 8px" @click="handleClose">取消</a-button>
    </template>
  </a-drawer>
  <a-modal v-model:open="openModal" title="附件" :footer="null">
    <div class="min-h-[250px]">
      <div v-for="item in shipmentDetails.files" :key="item.id" class="cursor-pointer">
        <a-space class="cursor-point" @click="previewAuthFile(item)">
          <LinkOutlined />
          <span class="text-blue-500 font-size-14">{{ item.name }}</span>
        </a-space>
      </div>
    </div>
  </a-modal>
  <!-- 图片预览组件 -->
  <a-image
    :width="0"
    :style="{ display: 'none' }"
    :preview="{
      visible: imageVisible,
      onVisibleChange: setImageVisible,
    }"
    :src="previewImageUrl"
  />
</template>

<script setup lang="ts">
import { getShipOrderDetail, getShipOrderDetailList, getLogisticsCompanyList, editLogistics } from '@/servers/ShipmentManage'
import BaseImage from '@/components/BaseImage/index.vue'
import { message } from 'ant-design-vue'
import { LinkOutlined } from '@ant-design/icons-vue'

const VITE_APP_ENV = ref(import.meta.env.VITE_APP_ENV)
const userData = ref(JSON.parse(localStorage.getItem('userData') || '{}'))
// 发货单详情
const shipmentDetails = ref<any>({})
// 发货&入库明细数据
const tableData = ref<any[]>([
  {
    images_view_url: 'https://img.yzcdn.cn/vant/ipad.png',
    product_name: 'aa',
    category_name: '商品aa类目',
    supplier_product_number: '供应商商品编码aaaa',
    product_number: '平台商品编码XXXXX',
    specification: '规格aaaa',
    purchase_total_quantity: '采购总数aaaa',
    shipment_quantity: '已发货数(已预约入库数)aaaa',
    purchase_price: '该次发货数(该次预约入库数)aaaa',
    in_stock_quantity: '入库数aaaa',
  },
])
// 发货&入库明细
const columns = ref<any[]>([
  {
    title: '序号',
    dataIndex: 'index',
    customRender: ({ index }) => index + 1,
    align: 'center',
  },
  // {
  //   title: '主图  ',
  //   dataIndex: 'image_url',
  // },
  {
    title: '商品',
    dataIndex: 'sku_name',
  },
  // {
  //   title: '商品类目',
  //   dataIndex: 'all_category',
  // },
  {
    title: '商品编码',
    dataIndex: 'srs_supplier_prod_code',
  },
  // {
  //   title: '平台商品编码',
  //   dataIndex: 'srs_platform_prod_code',
  // },
  {
    title: '规格',
    dataIndex: 'type_specification',
  },
  {
    title: '采购总数',
    dataIndex: 'total_purchase_quantity',
  },
  {
    title: '已发货数(已预约入库数)',
    dataIndex: 'total_purchase_scheduled_quantity',
    align: 'center',
  },
  {
    title: '该次发货数(该次预约入库数)',
    dataIndex: 'scheduled_quantity',
    align: 'center',
  },
  {
    title: '入库数',
    dataIndex: 'total_actual_inbound',
  },
])
// 抽屉是否打开
const openDrawer = ref(false)
const tableIds = ref<number[]>([])
// 当前的id
const selectId = ref()
const companySupplierId = ref()
// 表单类型：detail、editLogist
const formType = ref('detail')
const formRef = ref()
const logisticsCompanyList = ref<any[]>([])
const editLogistRules = {
  logistics_company_number: [{ required: true, message: '请选择物流公司' }],
  tracking_number: [{ required: true, message: '请输入物流单号', trigger: ['blur', 'change'] }],
}
const openModal = ref(false)
const imageVisible = ref(false)
const previewImageUrl = ref('')

const rules = ref()

// 显示抽屉
const showDrawer = async (id: number, company_supplier_id: number, ids: number[], type: string) => {
  formType.value = type
  selectId.value = id
  openDrawer.value = true
  tableIds.value = ids
  companySupplierId.value = company_supplier_id
  // 不需要传company_supplier_id
  // const params = { id, company_supplier_id }
  const params = { id }
  console.log('showDrawer-params', params)
  getShipmentDetail(params)
  getShipmentDetailList(params)
  if (type == 'editLogist') {
    rules.value = JSON.parse(JSON.stringify(editLogistRules))
  }
}

// 关闭抽屉
const handleClose = () => {
  openDrawer.value = false
}

const onSubmit = async () => {
  await formRef.value.validate()
  const params = {
    id: selectId.value,
    logistics_company_number: shipmentDetails.value.logistics_company_number,
    tracking_number: shipmentDetails.value.tracking_number,
    // company_supplier_id: companySupplierId.value,
  }
  editLogistics(params).then((res) => {
    console.log('editLogistics', res)
    message.success('修改成功')
    handleClose()
  })
}

// 获取发货单详情
const getShipmentDetail = async (params) => {
  const res = await getShipOrderDetail(params)
  shipmentDetails.value = res.data
  shipmentDetails.value.receiver_address = res.data.warehouse_receiver_province + res.data.warehouse_receiver_city + res.data.warehouse_receiver_area + res.data.warehouse_receiver_address
  console.log('getShipmentDetail', res)
}

// 获取发货单详情列表
const getShipmentDetailList = async (params) => {
  const reqParams = {
    // "page": 1,
    // "pageSize": 10,
    booking_order_id: params.id,
    is_check: true,
    // company_supplier_id: params.company_supplier_id,
  }
  const res = await getShipOrderDetailList(reqParams)
  tableData.value = res.data.list
}

// 上一条/下一条切换
const handleChangeInfo = (type: 'prev' | 'next') => {
  const index = tableIds.value.findIndex((id) => id === selectId.value)
  if (index === 0 && type === 'prev') {
    message.error('已经是第一条')
    return
  }
  if (index === tableIds.value.length - 1 && type === 'next') {
    message.error('已经是最后一条')
    return
  }

  shipmentDetails.value.logistics_company_number = ''
  shipmentDetails.value.tracking_number = ''

  if (type === 'prev') {
    selectId.value = tableIds.value[index - 1]
  } else {
    selectId.value = tableIds.value[index + 1]
  }
  const params = {
    id: selectId.value,
    // company_supplier_id: companySupplierId.value,
  }
  getShipmentDetail(params)
  getShipmentDetailList(params)
}

getLogisticsCompanyList({
  page: 1,
  pageSize: 500,
}).then((res) => {
  console.log(222, res)
  logisticsCompanyList.value = res.data?.list
})

const onClickFiles = () => {
  console.log('shipmentDetails.files', shipmentDetails.value.files)
  openModal.value = true
}

// 图片预览控制
const setImageVisible = (visible: boolean) => {
  imageVisible.value = visible
}

const previewAuthFile = async (files) => {
  const fileId = files.id
  if (!fileId) {
    message.warning('文件不存在')
    return
  }

  try {
    let url = ''
    if (VITE_APP_ENV.value == 'development') {
      url = `${window.location.origin}/api/api/Files/ViewByFileId?fileId=${fileId}`
    } else {
      url = `${window.location.origin}/api/Files/ViewByFileId?fileId=${fileId}`
    }

    const response = await fetch(url, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/octet-stream',
        logintoken: userData.value.login_token,
      },
    })

    if (!response.ok) {
      message.warning('获取授权书失败')
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    const blob = await response.blob()
    const previewUrl = URL.createObjectURL(blob)

    // 检查文件类型
    const contentType = response.headers.get('content-type') || ''
    if (contentType.startsWith('image/')) {
      // 图片文件使用图片预览组件
      previewImageUrl.value = previewUrl
      imageVisible.value = true
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    } else {
      // PDF等其他文件在新窗口打开
      window.open(previewUrl, '_blank')
      setTimeout(() => URL.revokeObjectURL(previewUrl), 30000)
    }
  } catch (error) {
    console.error('授权书预览失败:', error)
    // message.error('授权书预览失败')
  }
}

defineExpose({
  showDrawer,
})
</script>

<style lang="scss" scoped>
:deep(.ant-table-cell) {
  white-space: nowrap;
}
</style>
