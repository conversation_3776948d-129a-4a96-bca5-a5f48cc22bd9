<template>
  <div class="main">
    <SearchForm v-model:form="formArr" :page-type="PageType.ORDER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />
    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.ORDER_MANAGE" :get-list="getList" :merge-cells="mergeCells">
      <template #total_scheduled_quantity_header>
        <div class="w-full flex flex-col items-center justify-center lh-16px">
          <div>已发货数</div>
          <div>（已预约入库数）</div>
        </div>
      </template>
      <template #shop="{ row }">
        <div class="flex items-center vxe-render-image">
          <BaseImage :src="row.image_url" />
          <div class="ml-8 c-#333" :style="{ lineHeight: autoLineHeight + 'px' }">
            <div>{{ row.sku_name }}</div>
            <div>
              <span class="c-#999 mr-4">类目:</span>
              <span>{{ row.all_category || '--' }}</span>
            </div>
          </div>
        </div>
      </template>
      <template #shop_code="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span class="c-#999 mr-4">供应商:</span>
            <span>{{ row.srs_supplier_prod_code || '--' }}</span>
          </div>
          <div>
            <span class="c-#999 mr-4">平台:</span>
            <span>{{ row.srs_platform_prod_code || '--' }}</span>
          </div>
        </div>
      </template>
      <template #order_status="{ row }">
        <BaseBadge v-bind="formatOrderStatus(row.order_status)" />
      </template>
      <template #shipment_status_str="{ row }">
        <BaseBadge v-if="row.shipment_status_str" :label="row.shipment_status_str" :type="shipmentStatusMap[row.shipment_status]" />
        <span v-else>--</span>
      </template>
      <template #purchase_status_str="{ row }">
        <BaseBadge v-if="row.purchase_status_str" :label="row.purchase_status_str" :type="purchaseStatusMap[row.purchase_status]" />
        <span v-else>--</span>
      </template>
      <template #purchase="{ row }">
        <div :style="{ lineHeight: autoLineHeight + 'px' }">
          <div>
            <span class="c-#999 mr-4">采购员:</span>
            <span>{{ row.buyer_name || '--' }}</span>
          </div>
          <div>
            <span class="c-#999 mr-4">采购时间:</span>
            <span>{{ row.purchase_time || '--' }}</span>
          </div>
        </div>
      </template>
      <template #operate="{ row }">
        <a-button :disabled="!btnPermission[910001]" type="text" @click="handleDetail(row)">详情</a-button>
      </template>
    </BaseTable>
    <OrderDetailDrawer ref="orderDetailDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import SearchForm from '@/components/SearchForm/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import BaseBadge from '@/components/BaseBadge/index.vue'
import BaseImage from '@/components/BaseImage/index.vue'
import { PageType } from '@/common/enum'
import { getOrderList } from '@/servers/OrderManage'
import { VxeTablePropTypes } from 'vxe-table'
import OrderDetailDrawer from './components/OrderDetailDrawer.vue'

const autoLineHeight = computed(() => (tableRef?.value?.lineHeightType === 1 ? 16 : 24))

const tableRef = useTemplateRef<InstanceType<typeof BaseTable>>('tableRef')
const orderDetailDrawerRef = useTemplateRef<InstanceType<typeof OrderDetailDrawer>>('orderDetailDrawerRef')

const mergeCells = ref<VxeTablePropTypes.MergeCells>([])

const { btnPermission } = usePermission()

const search = () => tableRef.value?.search()
// 发货状态颜色
const shipmentStatusMap = {
  10: 'warning',
  20: 'info',
  30: 'success',
}

const purchaseStatusMap = {
  0: 'warning',
  1: 'info',
  2: 'success',
}

const formArr = ref<any>([
  {
    label: '订单状态',
    key: 'order_status',
    value: null,
    type: '',
    isQuicks: true,
    options: [
      { label: '进行中', value: 1 },
      { label: '已完成', value: 2 },
      { label: '已关闭', value: 3 },
      { label: '变更中', value: 5 },
    ],
  },
  {
    label: '发货状态',
    key: 'shipment_status',
    value: null,
    type: '',
    isQuicks: true,
    options: [
      { label: '未发货', value: 10 },
      { label: '部分发货', value: 20 },
      { label: '全部发货', value: 30 },
    ],
  },
  {
    label: '收货状态',
    key: 'purchase_status',
    value: null,
    type: '',
    isQuicks: true,
    options: [
      { label: '待入库', value: '0' },
      { label: '部分入库', value: 1 },
      { label: '完全入库', value: 2 },
    ],
  },
  {
    label: '采购订单编号',
    key: 'number',
    value: null,
    type: 'batch-input',
  },
  {
    label: '供应商商品编码',
    key: 'srs_supplier_prod_code',
    value: null,
    type: 'batch-input',
  },
  {
    label: '平台商品编码',
    key: 'srs_platform_prod_code',
    value: null,
    type: 'batch-input',
  },
  {
    label: '商品名称',
    key: 'product_name',
    value: null,
    type: 'input',
  },
  {
    label: '采购员',
    key: 'buyer_name',
    value: null,
    type: 'input',
  },
  {
    label: '收货仓库',
    key: 'warehourse_name',
    value: null,
    type: 'input',
  },
  {
    label: '采购时间',
    type: 'range-picker',
    formKeys: ['purchase_start_time', 'purchase_end_time'],
    placeholder: ['采购开始时间', '采购结束时间'],
  },
  {
    label: '协议到货时间',
    type: 'range-picker',
    formKeys: ['predict_delivery_date_start', 'predict_delivery_date_end'],
    placeholder: ['协议到货开始时间', '协议到货结束时间'],
  },
])

const getList = async (params: any) => {
  if (params.order_status) params.order_status = [params.order_status]
  if (params.shipment_status) params.shipment_status = [params.shipment_status]
  if (params.purchase_status) params.purchase_status = [params.purchase_status]
  if (params.srs_supplier_prod_code) params.srs_supplier_prod_code = params.srs_supplier_prod_code.split(',')
  if (params.srs_platform_prod_code) params.srs_platform_prod_code = params.srs_platform_prod_code.split(',')
  const res = await getOrderList(params)
  const columns = tableRef.value?.tableRef?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = res.data.list.flatMap((item: any, index: number) => {
    if (item.purchaseOrderDetails?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = [
          'shop',
          'shop_code',
          'srs_supplier_prod_code',
          'srs_platform_prod_code',
          'total_purchase_quantity',
          'purchase_tax_price',
          'other_fees',
          'total_scheduled_quantity',
          'purchase_inbound_quantity',
          'predict_delivery_date',
        ]
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.purchaseOrderDetails.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.purchaseOrderDetails.length - 1
    }
    if (!item.purchaseOrderDetails?.length) {
      return {
        ...item,
        idx: index + 1 + (tableRef.value!.page - 1) * tableRef.value!.pageSize,
        pId: item.id,
      }
    }
    return item.purchaseOrderDetails.map((product: any) => {
      product.pId = item.id
      return {
        ...item,
        ...product,
        idx: index + 1 + (tableRef.value!.page - 1) * tableRef.value!.pageSize,
      }
    })
  })
  nextTick(() => {
    mergeCells.value = mergeList
    // 设置行高
  })
  return {
    data: {
      list,
      total: res.data.total,
    },
  }
}

const formatOrderStatus = (status: number) => {
  const statusMap = {
    1: { type: 'info', label: '进行中' },
    2: { type: 'success', label: '已完成' },
    3: { type: 'default', label: '已关闭' },
    5: { type: 'warning', label: '变更中' },
  }
  return statusMap[status]
}

// 查看详情
const handleDetail = (row: any) => {
  const useData = localStorage.getItem('userData')
  const userData = useData ? JSON.parse(useData) : {}
  const ids = tableRef.value?.tableData.map((i) => i.pId)
  const uniqueIds = [...new Set(ids)] as number[]
  orderDetailDrawerRef.value?.show(row.pId, userData.id, uniqueIds)
}
</script>

<style scoped lang="scss">
:deep(.table-box) {
  .vxe-table--header {
    height: 38px;
  }
}
</style>
