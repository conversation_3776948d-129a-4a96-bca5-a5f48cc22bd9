const otherRouter = [
  {
    path: '/404',
    name: '404',
    component: () => import('@/views/error/404.vue'),
  },
  {
    path: '/systemManagement',
    title: '系统管理',
    name: '系统管理',
    children: [
      {
        path: '/roleManagement',
        title: '角色管理',
        name: '角色管理',
        component: () => import('@/views/pageComponents/systemManagement/roleManagement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/watermarkManagement',
        title: '通用设置',
        name: '通用设置',
        component: () => import('@/views/pageComponents/systemManagement/watermarkManagement/index.vue'),

        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/clientModule',
    title: '用户管理1',
    name: '用户管理1',
    children: [
      {
        path: '/userLists',
        title: '用户管理',
        name: '用户管理',
        component: () => import('@/views/pageComponents/clientModule/userList/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/basicInfoManagement',
    title: '基础信息',
    name: '基础信息',
    children: [
      {
        path: '/companyInfo',
        title: '企业资料',
        name: '企业资料',
        component: () => import('@/views/pageComponents/basicInfoManagement/companyInfo/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/supplierSettlement',
    title: '供应商管理',
    name: '供应商管理',
    children: [
      {
        path: '/supplierSettlement',
        title: '供应商入驻',
        name: '供应商入驻',
        component: () => import('@/views/pageComponents/supplierManagement/supplierSettlement/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/productManagement',
    title: '商品管理',
    name: '商品管理',
    children: [
      {
        path: '/brand',
        title: '品牌库',
        name: '品牌库',
        component: () => import('@/views/pageComponents/productManagement/brand/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
      {
        path: '/ProductInfo',
        title: '商品库',
        name: '商品库',
        component: () => import('@/views/pageComponents/productManagement/productLibrary/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
      {
        path: '/certificateList',
        title: '资质/证书管理',
        name: '资质/证书管理',
        component: () => import('@/views/pageComponents/productManagement/qualificationCertificate/index.vue'),
        meta: {
          KeepAlive: false,
        },
      },
      {
        path: '/productStock',
        title: '商品库存',
        name: '商品库存',
        component: () => import('@/views/pageComponents/productManagement/productStock/index.vue'),
        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/notifyManagement',
    title: '通知管理',
    name: '通知管理',
    children: [
      {
        path: '/systemNotify',
        title: '系统通知',
        name: '系统通知',
        component: () => import('@/views/pageComponents/notifyManagement/systemNotify/index.vue'),

        meta: {
          KeepAlive: true,
        },
      },
    ],
  },
  {
    path: '/complianceManagement',
    title: '订单中心',
    name: '订单中心',
    children: [
      {
        path: '/orderManagement',
        title: '订单管理',
        name: '订单管理',
        component: () => import('@/views/pageComponents/orderManagement/orderManage/index.vue'),
      },
      {
        path: '/shipmentManagement',
        title: '发货单管理',
        name: '发货单管理',
        component: () => import('@/views/pageComponents/orderManagement/shipmentManage/index.vue'),
      },
      {
        path: '/returnManagement',
        title: '退换单管理',
        name: '退换单管理',
        component: () => import('@/views/pageComponents/orderManagement/returnOrderManage/index.vue'),
      },
    ],
  },
]
export default otherRouter
