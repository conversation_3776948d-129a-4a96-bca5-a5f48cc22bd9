<template>
  <div class="main">
    <SearchForm v-model:form="formArr" :page-type="PageType.RETURN_ORDER_MANAGE" @search="search" @setting="tableRef?.showTableSetting()" />

    <BaseTable ref="tableRef" v-model:form="formArr" :page-type="PageType.RETURN_ORDER_MANAGE" :is-index="true" :get-list="getList" :form-format="formFormat" :merge-cells="mergeCells">
      <!-- 退库申请单编号 -->
      <template #number="{ row }">
        <div class="flex items-center">
          {{ row.number || '--' }}
          <CopyOutlined v-if="row.number" class="copy-icon" @click="copyToClipboard(row.number, '退库申请单编号')" />
        </div>
      </template>

      <!-- 关联采购订单编号 -->
      <template #purchase_order_numbers="{ row }">
        <div v-if="row.purchase_order_numbers && row.purchase_order_numbers.length > 0">
          <div v-for="(orderNum, index) in row.purchase_order_numbers" :key="index" class="mb-1 flex items-center">
            {{ orderNum }}
            <CopyOutlined class="copy-icon" @click="copyToClipboard(orderNum, '采购订单编号')" />
          </div>
        </div>
        <div v-else>--</div>
      </template>

      <!-- 主图 -->
      <template #main_image="{ row }">
        <div class="main_image">
          <!-- 严格判断是否有有效图片 -->
          <a-image
            v-if="row.currentDetail && row.currentDetail.image_url && row.currentDetail.image_url.trim()"
            :src="row.currentDetail.image_url"
            :class="lineHeightType == 1 ? '!w-40 !h-30' : lineHeightType == 2 ? '!w-50 !h-40' : '!w-60 !h-50'"
            alt="商品图片"
            class="product-image clickable-image"
            :preview="{
              onVisibleChange: (previewVisible) => setPreviewVisible(row, previewVisible),
              src: row.currentDetail.image_url,
            }"
            @error="handleImageError(row)"
            @load="handleImageLoad(row)"
          >
            <template #previewMask>
              <EyeOutlined />
            </template>
          </a-image>

          <!-- 加载状态 -->
          <div v-else-if="isImageLoading(row)" class="image-loading">
            <LoadingOutlined />
          </div>

          <!-- 无图片时显示 -->
          <div v-else class="no-image">--</div>
        </div>
      </template>

      <!-- 退货商品 -->
      <template #sku_name="{ row }">
        <div class="font-medium">{{ row.currentDetail?.sku_name || '--' }}</div>
      </template>

      <!-- 商品类目 -->
      <template #all_category="{ row }">
        <div class="text-sm">{{ row.currentDetail?.all_category || '--' }}</div>
      </template>

      <!-- 供应商商品编码 -->
      <template #srs_supplier_prod_code="{ row }">
        <div class="text-sm">{{ row.currentDetail?.srs_supplier_prod_code || '--' }}</div>
      </template>

      <!-- 平台商品编码 -->
      <template #srs_platform_prod_code="{ row }">
        <div class="text-sm">{{ row.currentDetail?.srs_platform_prod_code || '--' }}</div>
      </template>

      <!-- 退库数量 -->
      <template #return_quantity="{ row }">
        <div class="text-right">{{ row.currentDetail?.return_quantity || 0 }}</div>
      </template>

      <!-- 退库金额 -->
      <template #return_amount="{ row }">
        <div class="text-right">¥{{ (row.currentDetail?.return_amount || 0).toFixed(2) }}</div>
      </template>

      <!-- 其他费用 -->
      <template #other_fee_amount="{ row }">
        <div class="text-right">¥{{ (row.other_fee_amount || 0).toFixed(2) }}</div>
      </template>

      <!-- 退库总金额 -->
      <template #total_return_amount="{ row }">
        <div class="text-right">¥{{ (row.total_return_amount || 0).toFixed(2) }}</div>
      </template>

      <!-- 申请类型 -->
      <template #application_type_string="{ row }">
        <div>{{ row.application_type_string || '--' }}</div>
      </template>

      <!-- 退货原因 -->
      <template #return_reason_type_string="{ row }">
        <div>{{ row.return_reason_type_string || '--' }}</div>
      </template>

      <!-- 仓库名称 -->
      <template #warehouse_name="{ row }">
        <div>{{ row.warehouse_name || '--' }}</div>
      </template>

      <!-- 收货人 -->
      <template #consignee="{ row }">
        <div>{{ row.consignee || '--' }}</div>
      </template>

      <!-- 单据状态 -->
      <template #order_status_string="{ row }">
        <div>{{ row.order_status_string || '--' }}</div>
      </template>

      <!-- 申请人 -->
      <template #creator_name="{ row }">
        <div>{{ row.creator_name || '--' }}</div>
      </template>

      <!-- 申请时间 -->
      <template #create_at="{ row }">
        <div>{{ row.create_at || '--' }}</div>
      </template>

      <!-- 操作 -->
      <template #operate="{ row }">
        <a-button type="link" size="small" @click="handleView(row)" :disabled="!btnPermission[930001]">查看</a-button>
      </template>
    </BaseTable>

    <!-- 详情抽屉 -->
    <ReturnOrderDetailDrawer ref="detailDrawerRef" />
  </div>
</template>

<script setup lang="ts">
import SearchForm from '@/components/SearchForm/index.vue'
import BaseTable from '@/components/BaseTable/index.vue'
import { PageType } from '@/common/enum'
import { GetReturnOrderList } from '@/servers/returnOrderManage'
import { message } from 'ant-design-vue'
import { LoadingOutlined, EyeOutlined, CopyOutlined } from '@ant-design/icons-vue'
import { ref, useTemplateRef, computed, watch, onMounted, nextTick } from 'vue'
import { VxeTablePropTypes } from 'vxe-table'
import { GetCommonOption } from '@/servers/Common'
import { usePermission } from '@/hook/usePermission'
import ReturnOrderDetailDrawer from './components/ReturnOrderDetailDrawer.vue'

const { btnPermission } = usePermission()

const tableRef = useTemplateRef<InstanceType<typeof BaseTable>>('tableRef')
const detailDrawerRef = useTemplateRef<InstanceType<typeof ReturnOrderDetailDrawer>>('detailDrawerRef')

// 合并单元格配置
const mergeCells = ref<VxeTablePropTypes.MergeCells>([])

// 图片状态管理
const imageStates = ref<{
  [key: number]: {
    loading: boolean
    error: boolean
  }
}>({})

// 根据表格行高类型动态调整图片大小
const lineHeightType = computed(() => tableRef?.value?.lineHeightType)

// 搜索表单配置
const formArr: any = ref([
  {
    label: '退货申请单编号',
    value: null,
    type: 'batch-input',
    key: 'number',
    width: 200,
    isShow: true,
  },
  {
    label: '采购订单编号',
    value: null,
    type: 'batch-input',
    key: 'purchase_order_numbers',
    width: 200,
    isShow: true,
  },
  {
    label: '供应商商品编码',
    value: null,
    type: 'batch-input',
    key: 'srs_supplier_prod_code',
    width: 200,
    isShow: true,
  },
  {
    label: '平台商品编码',
    value: null,
    type: 'batch-input',
    key: 'srs_platform_prod_code',
    width: 200,
    isShow: true,
  },
  {
    label: '商品名称',
    value: null,
    type: 'input',
    key: 'sku_name',
    isShow: true,
  },
  {
    label: '申请类型',
    value: null,
    type: 'select',
    key: 'application_type',
    isShow: true,
    options: [],
  },
  {
    label: '退货原因',
    value: null,
    type: 'select',
    key: 'return_reason_type',
    isShow: true,
    options: [],
  },
  {
    label: '单据状态',
    value: null,
    type: 'select',
    key: 'order_status',
    isShow: true,
    options: [],
  },
  {
    label: '申请人',
    value: null,
    type: 'input',
    key: 'creator_name',
    isShow: true,
  },
  {
    label: '申请时间',
    value: null,
    type: 'range-picker',
    key: 'create_time',
    formKeys: ['creator_start_time', 'creator_end_time'],
    placeholder: ['申请开始时间', '申请结束时间'],
    valueFormat: 'YYYY-MM-DD HH:mm:ss',
    isShow: true,
  },
])

// 表单格式化
const formFormat = (obj: any) => {
  const formattedObj = { ...obj }

  // 处理批量输入的字段 - 将逗号分隔的字符串转换为数组
  if (formattedObj.number && typeof formattedObj.number === 'string') {
    formattedObj.number = formattedObj.number.split(',').filter((item: string) => item.trim())
  }
  if (formattedObj.purchase_order_numbers && typeof formattedObj.purchase_order_numbers === 'string') {
    formattedObj.purchase_order_numbers = formattedObj.purchase_order_numbers.split(',').filter((item: string) => item.trim())
  }
  if (formattedObj.srs_supplier_prod_code && typeof formattedObj.srs_supplier_prod_code === 'string') {
    formattedObj.srs_supplier_prod_code = formattedObj.srs_supplier_prod_code.split(',').filter((item: string) => item.trim())
  }
  if (formattedObj.srs_platform_prod_code && typeof formattedObj.srs_platform_prod_code === 'string') {
    formattedObj.srs_platform_prod_code = formattedObj.srs_platform_prod_code.split(',').filter((item: string) => item.trim())
  }

  return formattedObj
}

const getList = async (params: any) => {
  const res = await GetReturnOrderList(params)
  const columns = tableRef.value?.tableRef?.getColumns() || []
  const mergeList: any[] = []
  mergeCells.value = []
  let findIndex = 0
  const list = res.data.list.flatMap((item: any, index: number) => {
    if (item.purchase_order_details?.length > 1) {
      // 为每个需要合并的列创建合并规则
      columns.forEach((col, colIndex) => {
        const mergeKeys = [
          'main_image', // 主图
          'sku_name', // 退货商品
          'all_category', // 商品类目
          'srs_supplier_prod_code', // 供应商商品编码
          'srs_platform_prod_code', // 平台商品编码
          'return_quantity', // 退库数量
          'return_amount', // 退库金额
        ]
        if (!mergeKeys.includes(col.field)) {
          mergeList.push({
            row: index + findIndex,
            col: colIndex,
            rowspan: item.purchase_order_details.length,
            colspan: 1,
          })
        }
      })
      findIndex += item.purchase_order_details.length - 1
    }
    // 如果有商品明细，按商品明细展示
    if (item.purchase_order_details && item.purchase_order_details.length > 0) {
      return item.purchase_order_details.map((product: any) => {
        product.pId = item.id
        return {
          ...item,
          currentDetail: product,
          ...product,
        }
      })
    }
    // 如果没有商品明细，显示一行空的商品信息
    return [
      {
        ...item,
        currentDetail: null,
      },
    ]
  })
  nextTick(() => {
    mergeCells.value = mergeList
    // 设置行高
  })
  return {
    data: {
      list,
      total: res.data.total,
    },
  }
}

// 查询
const search = () => {
  tableRef.value?.search()
  // 数据格式化函数会自动处理图片状态初始化，这里不需要额外处理
}

const getCertificateType = async () => {
  // 获取选品状态、审核状态、提审人、选品人数据
  const dropdownRes = await GetCommonOption({ types: [25, 26, 27] }) // SRM退换单申请类型= 25    SRM退换单退货原因=26  SRM退换单单据状态=27

  const selectionapplication_type = dropdownRes.data?.data?.[25] || []
  const return_reason_type = dropdownRes.data?.data?.[26] || [] // 提审人选项
  const order_status = dropdownRes.data?.data?.[27] || [] // 提审人选项

  formArr.value.forEach((item: any) => {
    if (item.key === 'application_type') {
      // 申请类型下拉数据
      item.options = selectionapplication_type
    }
    if (item.key === 'return_reason_type') {
      // 换单退货原因下拉数据
      item.options = return_reason_type
    }
    if (item.key === 'order_status') {
      // 退换单单据状态拉数据
      item.options = order_status
    }
  })
}

// 查看详情
const handleView = (row: any) => {
  // 获取当前页的所有数据
  const currentPageData = tableRef.value?.tableData || []

  // 如果没有数据，创建一些测试数据
  if (currentPageData.length === 0) {
    const testData = [
      { id: 1, number: 'TH000001', company_supplier_id: 1001 },
      { id: 2, number: 'TH000002', company_supplier_id: 1002 },
      { id: 3, number: 'TH000003', company_supplier_id: 1003 },
    ]
    const testIndex = testData.findIndex((item: any) => item.id === row.id) || 0
    detailDrawerRef.value?.open(row, testData, testIndex)
    return
  }

  // 找到当前行在数据中的索引
  const currentIndex = currentPageData.findIndex((item: any) => item.id === row.id)

  if (currentIndex === -1) {
    message.error('无法找到当前数据')
    return
  }

  // 打开详情抽屉
  detailDrawerRef.value?.open(row, currentPageData, currentIndex)
}

// 复制到剪贴板（兼容处理）
const copyToClipboard = async (text: string, label: string) => {
  try {
    // 现代浏览器支持 clipboard API
    if (navigator.clipboard && typeof navigator.clipboard.writeText === 'function') {
      await navigator.clipboard.writeText(text)
      message.success(`${label}已复制到剪贴板`)
      return
    }

    // 降级方案：创建临时文本框执行复制
    const textarea = document.createElement('textarea')
    textarea.value = text
    // 隐藏文本框（避免影响页面布局）
    textarea.style.position = 'fixed'
    textarea.style.top = '-999px'
    textarea.style.left = '-999px'
    document.body.appendChild(textarea)
    // 选中并复制
    textarea.select()
    document.execCommand('copy')
    // 清理临时元素
    document.body.removeChild(textarea)
    message.success(`${label}已复制到剪贴板`)
  } catch (error) {
    console.error('复制失败:', error)
    message.error('复制失败，请手动复制')
  }
}

// 图片预览逻辑
const setPreviewVisible = (_row: any, visible: boolean) => {
  // 仅处理可见状态变化
  if (visible) {
    // 可添加预览打开时的其他逻辑
  }
}

// 处理图片加载完成
const handleImageLoad = (row: any) => {
  const rowId = row.uniqueRowId || row.id
  if (imageStates.value[rowId]) {
    imageStates.value[rowId].loading = false
    imageStates.value[rowId].error = false
  }
}

// 处理图片加载错误
const handleImageError = (row: any) => {
  const rowId = row.uniqueRowId || row.id
  console.error(`商品图片加载失败: ${rowId}`)
  if (imageStates.value[rowId]) {
    imageStates.value[rowId].loading = false
    imageStates.value[rowId].error = true
  }
}

// 检查图片是否处于加载状态
const isImageLoading = (row: any) => {
  const rowId = row.uniqueRowId || row.id
  const hasImage = row.currentDetail?.image_url && row.currentDetail.image_url.trim()
  return !!hasImage && imageStates.value[rowId]?.loading === true
}

// 监听表格数据变化，更新图片状态
watch(
  () => tableRef.value?.tableData,
  (newData) => {
    if (newData) {
      newData.forEach((row: any) => {
        const rowId = row.uniqueRowId || row.id
        const hasImage = row.currentDetail?.image_url && row.currentDetail.image_url.trim()
        if (!imageStates.value[rowId]) {
          imageStates.value[rowId] = {
            loading: !!hasImage,
            error: false,
          }
        } else {
          imageStates.value[rowId].loading = !!hasImage
        }
      })
    }
  },
  { deep: true }, // 深度监听，确保数据变化时能触发
)

onMounted(async () => {
  getCertificateType()
})
</script>

<style scoped>
.main {
  padding: 20px;
}
/* 图片样式 */
.main_image {
  display: flex;
  align-items: center;
  justify-content: center;
}

.product-image {
  object-fit: contain;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  transition: all 0.3s ease;
}

.clickable-image {
  cursor: pointer;
}

.clickable-image:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 15%);
  transform: scale(1.05);
}
/** .no-image {
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  color: #999;
  background: #fafafa;
  border: 1px dashed #d9d9d9;
  border-radius: 4px;
  width: 100px;
  height: 80px;
} */

.image-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 80px;
  color: #1890ff;
}
/* 复制图标样式 */
.copy-icon {
  margin-left: 8px;
  font-size: 14px;
  color: #1890ff;
  cursor: pointer;
  transition: color 0.2s;

  &:hover {
    color: #40a9ff;
  }
}
/* 图片预览遮罩样式 */
:deep(.ant-image-preview-mask) {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
